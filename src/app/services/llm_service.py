import json
from typing import Dict, Optional
from langchain_openai import ChatOpenAI
import os

import src.app.config as config

from src.app.tools.lookup_cache import LookupCache


class LLMEntityExtractor:
    """Service for extracting entities using LLM with structured output."""

    def __init__(self, model: str = "gpt-4o-mini"):
        self.llm = ChatOpenAI(model=model, temperature=0.1, api_key=os.getenv("OPEN_AI_API_KEY"))

    def extract_entities(self, user_input: str) -> Dict[str, Optional[str]]:
        """Extract sector, country, and continent from user input using LLM."""

        # Get available options
        sectors = [{s["sector_name"]: s["sector_id"]} for s in LookupCache.get_sectors()]
        countries = [{c["country_name"]: c["country_id"]} for c in LookupCache.get_countries()]
        continents = [{c["continent_name"]: c["continent_id"]} for c in LookupCache.get_continents()]

        system_prompt = f"""Extract business entities from user queries. 

Available sectors: {sectors}...
Available countries: {countries}...
Available continents: {continents}

Extract: sector, country, continent from the user input. Then use the IDs and also return sector_id, country_id, continent_id.
Return JSON with these keys. Use null if not found. Do not include other stuff like ```json!!!
Be conservative - only extract what's clearly mentioned. Sometimes sectors could sound similar - Accounting Services vs Tax Advisors, be careful and reflect what to use!"""

        resp = self.llm.invoke([
            ("system", system_prompt),
            ("user", f"Extract entities: '{user_input}'")
        ])

        # content will be a JSON string
        content = resp.content
        print(f"LLM response: {content}")
        result = json.loads(content)

        return result



#x = LLMEntityExtractor()
#print(x.extract_entities("I'm looking for tax advisors in Germany"))