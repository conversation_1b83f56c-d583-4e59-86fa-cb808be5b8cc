from langchain_core.tools import tool
from src.app.tools.lookup_cache import find_country_id, find_continent_id, find_sector_id

@tool
def lookup_entity(entity_type: str, name: str) -> int | None:
    """
    Look up an ID from the catalog.
    entity_type: one of ['country', 'continent', 'sector']
    name: human readable name like 'Germany', 'Europe', 'Retail'
    Returns: integer ID or None if not found
    """
    if entity_type == "country":
        return find_country_id(name)
    elif entity_type == "continent":
        return find_continent_id(name)
    elif entity_type == "sector":
        return find_sector_id(name)
    return None
