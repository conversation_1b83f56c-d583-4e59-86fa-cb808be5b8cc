from typing import Dict
from ..services.api_client import DataPlatformClient
from ..services.llm_service import LLMEntityExtractor

# Initialize LLM entity extractor
_llm_extractor = LLMEntityExtractor()

# 0) extract current user input from messages
def prepare_input(state: Dict):
    messages = state.get("messages", [])
    if messages:
        # Get the last user message
        for msg in reversed(messages):
            if msg["role"] == "user":
                state["current_user_input"] = msg["content"]
                break
    return state

# 1) extract entities using LLM
def extract_entities(state: Dict):
    user_input = state.get("current_user_input") or ""

    # Use LLM to extract entities
    extracted = _llm_extractor.extract_entities(user_input)

    # Separate entity names and IDs
    entities = {}
    ids = {}

    for k, v in extracted.items():
        if v is not None:
            if k.endswith("_id"):
                ids[k] = v
            else:
                entities[k] = v

    state["entities"] = entities
    state["ids"] = ids
    return state


# 3) call API with resolved IDs
_client = DataPlatformClient()

def call_api(state: Dict):
    ids = state.get("ids", {})


    results = _client.get_targets_no_buyer(
        continent_id=ids.get("continent_id"),
        country_id=ids.get("country_id"),
        sector_id=ids.get("sector_id"),
        region=None,
        anonymized_companies=False,
    )
    state["results"] = results
    return state

# 4) format output
def format_response(state: Dict):
    results = state.get("results") or []
    if not results:
        response = "I couldn’t find results for that query."
    else:
        # limit to top 10 for readability
        top = results[:10]
        lines = [
            "Here are the top matches:",
            *[f"- {r.get('company_name')} ({r.get('locations_count')} locations)" for r in top],
        ]
        response = "\n".join(lines)

    state["current_response"] = response
    return state

# 5) update conversation history
def update_conversation(state: Dict):
    messages = state.get("messages", [])
    current_response = state.get("current_response", "")

    # Add AI response to conversation
    if current_response:
        messages.append({"role": "assistant", "content": current_response})

    state["messages"] = messages
    return state
