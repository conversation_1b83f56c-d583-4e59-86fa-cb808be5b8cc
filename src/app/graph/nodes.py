from typing import Dict
from ..services.api_client import DataPlatformClient
from ..services.llm_service import LLMEntityExtractor

# Initialize LLM entity extractor
_llm_extractor = LLMEntityExtractor()

# 1) extract entities using LLM
def extract_entities(state: Dict):
    user_input = state.get("user_input") or ""

    # Use LLM to extract entities
    extracted = _llm_extractor.extract_entities(user_input)

    # Separate entity names and IDs
    entities = {}
    ids = {}

    for k, v in extracted.items():
        if v is not None:
            if k.endswith("_id"):
                ids[k] = v
            else:
                entities[k] = v

    state["entities"] = entities
    state["ids"] = ids
    return state


# 3) call API with resolved IDs
_client = DataPlatformClient()

def call_api(state: Dict):
    ids = state.get("ids", {})


    results = _client.get_targets_no_buyer(
        continent_id=ids.get("continent_id"),
        country_id=ids.get("country_id"),
        sector_id=ids.get("sector_id"),
        region=None,
        anonymized_companies=False,
    )
    state["results"] = results
    return state

# 4) format output
def format_response(state: Dict):
    results = state.get("results") or []
    if not results:
        state["final_message"] = "I couldn’t find results for that query."
        return state

    # limit to top 10 for readability
    top = results[:10]
    lines = [
        "Here are the top matches:",
        *[f"- {r.get('company_name')} ({r.get('locations_count')} locations)" for r in top],
    ]
    state["final_message"] = "\n".join(lines)
    return state
