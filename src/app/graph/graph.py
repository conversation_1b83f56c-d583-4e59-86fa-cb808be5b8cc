from langgraph.graph import StateGraph
from src.app.graph.state import BotS<PERSON>
from src.app.graph.nodes import prepare_input, extract_entities, call_api, format_response, update_conversation

def build_graph():
    g = StateGraph(BotState)
    g.add_node("prepare_input", prepare_input)
    g.add_node("extract_entities", extract_entities)
    g.add_node("call_api", call_api)
    g.add_node("format_response", format_response)
    g.add_node("update_conversation", update_conversation)

    g.set_entry_point("prepare_input")
    g.add_edge("prepare_input", "extract_entities")
    g.add_edge("extract_entities", "call_api")
    g.add_edge("call_api", "format_response")
    g.add_edge("format_response", "update_conversation")
    g.set_finish_point("update_conversation")

    return g.compile()

graph = build_graph()
