from typing import Dict, Any, Optional, TypedDict, List

class Message(TypedDict):
    role: str  # "user" or "assistant"
    content: str

class BotState(TypedDict, total=False):
    messages: List[Message]          # conversation history
    current_user_input: str          # latest user message being processed
    entities: Dict[str, str]         # {"sector": "pharmacy", "country": "uk", "continent": "Europe"}
    ids: Dict[str, int]              # {"sector_id": 57, "country_id": 184, "continent_id": 4}
    results: List[Dict[str, Any]]
    current_response: str            # AI response being built
