from fastapi import FastAPI
from pydantic import BaseModel
from typing import List, Optional
from src.app.graph.graph import graph
from src.app.graph.state import Message

app = FastAPI(title="Target Search Bot")

class ChatRequest(BaseModel):
    user_input: str
    messages: Optional[List[Message]] = []

class ChatResponse(BaseModel):
    message: str
    messages: List[Message]

@app.post("/chat", response_model=ChatResponse)
def chat(req: ChatRequest):
    # Add user message to conversation
    messages = req.messages or []
    messages.append({"role": "user", "content": req.user_input})

    # Process through graph
    out = graph.invoke({"messages": messages})

    # Return latest AI response and full conversation
    final_messages = out.get("messages", [])
    latest_response = final_messages[-1]["content"] if final_messages else "No response"

    return ChatResponse(message=latest_response, messages=final_messages)
