from fastapi import FastAPI
from pydantic import BaseModel
from src.app.graph.graph import graph

app = FastAPI(title="Target Search Bot")

class ChatRequest(BaseModel):
    user_input: str

class ChatResponse(BaseModel):
    message: str

@app.post("/chat", response_model=ChatResponse)
def chat(req: ChatRequest):
    # minimal path: feed user text into the graph and return formatted message
    out = graph.invoke({"user_input": req.user_input})
    return ChatResponse(message=out.get("final_message", "No response"))
