#!/usr/bin/env python3
"""
Test script for the refactored LLM-based entity extraction.
"""

import os
import sys
sys.path.append('src')

from src.app.services.llm_service import LLMEntityExtractor
from src.app.graph.nodes import extract_entities, resolve_ids

def test_llm_extraction():
    """Test the LLM entity extractor directly."""
    print("Testing LLM Entity Extractor...")
    
    extractor = LLMEntityExtractor()
    
    test_queries = [
        "I'm looking for banks in the UK",
        "Find pharmacy companies in the United States",
        "Show me diversified retailers in Europe",
        "Looking for companies in Germany",
        "Find targets in the banking sector",
    ]
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        try:
            entities = extractor.extract_entities(query)
            print(f"Extracted: {entities}")
        except Exception as e:
            print(f"Error: {e}")

def test_graph_nodes():
    """Test the graph nodes with the new extraction."""
    print("\n" + "="*50)
    print("Testing Graph Nodes...")
    
    test_states = [
        {"user_input": "I'm looking for banks in the UK"},
        {"user_input": "Find pharmacy companies in the United States"},
        {"user_input": "Show me diversified retailers in Europe"},
    ]
    
    for state in test_states:
        print(f"\nInput: '{state['user_input']}'")
        try:
            # Test entity extraction
            result_state = extract_entities(state.copy())
            print(f"Entities: {result_state.get('entities', {})}")
            
            # Test ID resolution
            resolved_state = resolve_ids(result_state)
            print(f"IDs: {resolved_state.get('ids', {})}")
            
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    # Check if OpenAI API key is set
    if not os.getenv("OPENAI_API_KEY"):
        print("Warning: OPENAI_API_KEY not set. LLM extraction may fail.")
    
    test_llm_extraction()
    test_graph_nodes()
